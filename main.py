import modal
import subprocess
import time
import os
import signal
import sys

# Create the image from the Docker registry
image = modal.Image.from_registry("gyoridavid/short-video-maker:latest-tiny", add_python="3.11")

# Create secret with environment variables
secret = modal.Secret.from_dict({
    "LOG_LEVEL": "debug",
    "PEXELS_API_KEY": "TcOQlIrSdcd7E9td1xi88aUh1neIK5PWSTWi3SefbFGvWvqf3jyCgRVW"
})

app = modal.App("short-video-maker", image=image, secrets=[secret])

@app.function()
@modal.web_server(3123, startup_timeout=60.0)
def serve():
    """
    Start the Node.js server and keep it running.
    The @web_server decorator expects this function to start a server
    that binds to port 3123 and keeps running.
    """
    print("Starting short-video-maker server...")

    # Start the Node.js process
    process = subprocess.Popen(
        ["node", "dist/index.js"],
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True,
        bufsize=1
    )

    # Function to handle cleanup on exit
    def cleanup_handler(signum, frame):
        print("Received signal, terminating Node.js process...")
        process.terminate()
        process.wait()
        sys.exit(0)

    # Register signal handlers for graceful shutdown
    signal.signal(signal.SIGTERM, cleanup_handler)
    signal.signal(signal.SIGINT, cleanup_handler)

    print(f"Node.js process started with PID: {process.pid}")

    # Monitor the process and stream output
    try:
        while True:
            output = process.stdout.readline()
            if output:
                print(f"[Node.js] {output.strip()}")

            # Check if process is still running
            if process.poll() is not None:
                print("Node.js process has exited")
                break

            time.sleep(0.1)
    except KeyboardInterrupt:
        print("Interrupted, cleaning up...")
        process.terminate()
        process.wait()
    except Exception as e:
        print(f"Error monitoring process: {e}")
        process.terminate()
        process.wait()
        raise