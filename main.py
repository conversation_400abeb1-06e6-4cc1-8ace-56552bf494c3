import modal
import subprocess

image = modal.Image.from_registry("gyoridavid/short-video-maker:latest-tiny", add_python="3.11").pip_install("fastapi[standard]")

secret = modal.Secret.from_dict({
    "LOG_LEVEL": "debug",
    "PEXELS_API_KEY": "TcOQlIrSdcd7E9td1xi88aUh1neIK5PWSTWi3SefbFGvWvqf3jyCgRVW"
})

app = modal.App(image=image, secrets=[secret])

@app.function()
@modal.web_server(3123, startup_timeout=60.0)
def serve():
    subprocess.Popen("pnpm start", shell=True)