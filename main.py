import modal
import subprocess
import time
import os
import signal
import sys

# Create the image from the Docker registry
image = modal.Image.from_registry("gyoridavid/short-video-maker:latest-tiny", add_python="3.11").

# Create secret with environment variables
# These match the environment variables expected by the short-video-maker application
secret = modal.Secret.from_dict({
    "LOG_LEVEL": "debug",
    "PEXELS_API_KEY": "TcOQlIrSdcd7E9td1xi88aUh1neIK5PWSTWi3SefbFGvWvqf3jyCgRVW",
    "DEV": "false",
    # Additional environment variables from the Dockerfile
    "DATA_DIR_PATH": "/app/data",
    "DOCKER": "true",
    "WHISPER_MODEL": "base.en",
    "CONCURRENCY": "1",
    "VIDEO_CACHE_SIZE_IN_BYTES": "2097152000"
})

app = modal.App("short-video-maker", image=image, secrets=[secret])

@app.function()
@modal.web_server(3123, startup_timeout=120.0)
def serve():
    """
    Start the Node.js server and keep it running.
    The @web_server decorator expects this function to start a server
    that binds to port 3123 and keeps running.
    """
    print("Starting short-video-maker server...")
    print("Current working directory:", os.getcwd())
    print("Contents of current directory:", os.listdir("."))

    # Change to the app directory where the Node.js application is located
    os.chdir("/app")
    print("Changed to /app directory")
    print("Contents of /app directory:", os.listdir("."))

    # Start the Node.js process using pnpm start (as defined in the Dockerfile CMD)
    # This should be equivalent to running the container's default command
    process = subprocess.Popen(
        ["pnpm", "start",],
        stdout=subprocess.PIPE,
        stderr=subprocess.STDOUT,
        universal_newlines=True,
        bufsize=1,
        cwd="/app"
    )

    # Function to handle cleanup on exit
    def cleanup_handler(signum, frame):
        print("Received signal, terminating Node.js process...")
        process.terminate()
        process.wait()
        sys.exit(0)

    # Register signal handlers for graceful shutdown
    signal.signal(signal.SIGTERM, cleanup_handler)
    signal.signal(signal.SIGINT, cleanup_handler)

    print(f"Node.js process started with PID: {process.pid}")

    # Monitor the process and stream output
    try:
        while True:
            output = process.stdout.readline()
            if output:
                print(f"[Node.js] {output.strip()}")

            # Check if process is still running
            if process.poll() is not None:
                print("Node.js process has exited")
                break

            time.sleep(0.1)
    except KeyboardInterrupt:
        print("Interrupted, cleaning up...")
        process.terminate()
        process.wait()
    except Exception as e:
        print(f"Error monitoring process: {e}")
        process.terminate()
        process.wait()
        raise